# Cloudflare Workers 部署指南

## 重要说明

由于Cloudflare Workers目前对Python的支持有限制，**无法在生产环境中使用外部包**（如aspose-cells-python）。本项目提供了两个版本：

1. **本地开发版本**：使用FastAPI，支持完整功能
2. **生产环境版本**：仅使用Python标准库，功能受限

## 前提条件

1. Node.js 和 npm
2. Python 3.8+
3. Cloudflare 账户

## 安装步骤

### 1. 安装Wrangler CLI

```bash
npm install -g wrangler
```

### 2. 登录Cloudflare

```bash
wrangler login
```

### 3. 本地开发

安装Python依赖（仅本地开发）：

```bash
pip install -r requirements-cloudflare.txt
```

启动本地开发服务器：

```bash
wrangler dev
```

访问 http://localhost:8787 测试API。

### 4. 部署到生产环境

```bash
wrangler deploy
```

## API 接口

### 健康检查
- **GET** `/api/health` 或 `/health`
- 返回服务状态信息

### 根路径
- **GET** `/`
- 返回服务基本信息

### 文件转换（受限）
- **POST** `/api/excel-to-pdf/convert` 或 `/convert`
- **注意**：生产环境中此接口返回501错误，因为无法使用Aspose库

## 限制和替代方案

### 当前限制
- 无法在生产环境使用aspose-cells-python
- 无法处理实际的Excel转PDF转换
- 文件上传功能受限

### 推荐替代方案

1. **Cloudflare Containers**（付费）
   - 支持完整的Python环境
   - 可以使用所有外部包

2. **其他平台**
   - Vercel（已配置）
   - Heroku
   - AWS Lambda
   - Railway

3. **混合架构**
   - 使用Cloudflare Workers处理轻量级API
   - 将Excel转换功能部署到其他支持完整Python的平台

## 开发建议

1. 在本地使用FastAPI开发和测试
2. 生产环境考虑使用其他平台
3. 关注Cloudflare Workers Python支持的更新

## 相关链接

- [Cloudflare Workers Python文档](https://developers.cloudflare.com/workers/languages/python/)
- [Cloudflare Containers](https://developers.cloudflare.com/workers/runtime-apis/containers/)
- [Python Workers示例](https://github.com/cloudflare/python-workers-examples)
