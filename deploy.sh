#!/bin/bash

echo "🚀 部署到Cloudflare Workers..."

# 检查wrangler是否安装
if ! command -v wrangler &> /dev/null; then
    echo "❌ Wrangler CLI未安装"
    echo "💡 请运行: npm install -g wrangler"
    exit 1
fi

# 检查是否已登录
if ! wrangler whoami &> /dev/null; then
    echo "❌ 未登录Cloudflare"
    echo "💡 请运行: wrangler login"
    exit 1
fi

echo "📋 当前配置:"
echo "   项目名称: excel-service-cf"
echo "   入口文件: src/main.py"
echo "   兼容日期: 2025-08-12"

echo ""
echo "⚠️  重要提醒:"
echo "   - 生产环境无法使用aspose-cells-python"
echo "   - Excel转PDF功能将返回501错误"
echo "   - 仅支持基础API功能"

echo ""
read -p "是否继续部署? (y/N): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🔄 开始部署..."
    wrangler deploy
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "✅ 部署成功!"
        echo "🌐 访问你的服务: https://excel-service-cf.你的账户.workers.dev"
        echo "🔍 健康检查: https://excel-service-cf.你的账户.workers.dev/health"
    else
        echo "❌ 部署失败"
        exit 1
    fi
else
    echo "❌ 部署已取消"
fi
