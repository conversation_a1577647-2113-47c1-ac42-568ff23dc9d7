# Excel转PDF服务 - Aspose专业版

专业Excel转PDF转换REST API服务，支持**真正删除水印**功能。

为 [7788.tools](https://7788.tools) 前端提供高质量的Excel转PDF转换服务。

## 🚀 快速启动

### 方法一：直接启动（开发环境）

```bash
# 1. 安装Python依赖
pip install -r requirements.txt

# 2. 启动服务
python main.py
```

服务启动后访问：http://localhost:8002

### 方法二：一键启动脚本（最简单）

```bash
# 自动检查环境、安装依赖并启动
./start.sh
```

脚本会自动：
- ✅ 检查Python版本
- ✅ 检测服务是否已运行
- ✅ 安装缺失依赖
- ✅ 创建必要目录
- ✅ 启动服务

**智能检测：** 如果服务已在运行，脚本会提示当前状态而不重复启动

### 方法三：部署脚本（生产环境）

```bash
# 运行部署脚本，选择部署方案
./scripts/deploy.sh
```

支持多种部署方案：
- 🐳 Docker 本地部署
- 🌐 云服务器部署
- 🚄 Railway 云平台
- 🎨 Render 云平台

## 📋 核心功能

- **Aspose.Cells专业引擎**：业界最高质量Excel转PDF转换
- **真正删除水印**：使用PyMuPDF redact技术彻底删除评估版水印
- **REST API**：简洁易用的HTTP接口
- **默认开启**：自动启用水印删除，无需手动配置

## 🔧 API使用

### 直接转换（推荐）
```bash
curl -X POST "http://localhost:8002/api/excel-to-pdf/convert" \
  -F "file=@example.xlsx"
```

### 控制水印删除
```bash
curl -X POST "http://localhost:8002/api/excel-to-pdf/convert?remove_watermark=false" \
  -F "file=@example.xlsx"
```

### 健康检查
```bash
curl "http://localhost:8002/api/health"
```

### API文档
访问 http://localhost:8002/docs 查看完整API文档

## 📋 启动检查清单

### 环境要求
- ✅ Python 3.8+ 已安装
- ✅ pip 包管理器可用
- ✅ 8002端口未被占用

### 启动步骤详解

1. **检查Python版本**
   ```bash
   python --version  # 应该显示 3.8 或更高版本
   ```

2. **安装依赖（首次运行）**
   ```bash
   pip install -r requirements.txt
   ```

3. **启动服务**
   ```bash
   python main.py
   ```

4. **验证服务状态**
   ```bash
   # 方法一：访问健康检查接口
   curl http://localhost:8002/api/health
   
   # 方法二：在浏览器中打开
   # http://localhost:8002/api/health
   ```

5. **查看API文档（可选）**
   - 访问：http://localhost:8002/docs
   - 或：http://localhost:8002/redoc

### 常见启动问题

**端口占用错误**
```bash
# 查看端口占用
lsof -i :8002

# 结束占用进程
kill -9 <PID>
```

**依赖安装失败**
```bash
# 升级pip
pip install --upgrade pip

# 使用清华源安装
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

## ⚙️ 配置要求

- Python 3.8+
- PyMuPDF（水印删除）
- Aspose.Cells许可证文件：`Aspose.Total.NET.lic`（可选）

## ✨ 水印删除特性

本服务的核心特色是**真正删除Aspose水印**：
- 使用redaction技术从PDF结构中完全移除水印文字
- 支持所有Aspose.Cells评估版水印内容
- 处理后PDF文件更小更干净
- 默认启用，无需额外配置

## 🔧 服务管理

### 启动服务
```bash
# 一键启动（推荐）
./start.sh

# 或手动启动
python main.py
```

### 停止服务
```bash
# 一键停止（推荐）
./stop.sh

# 或手动停止
# 按 Ctrl + C 停止服务
# 或发送 SIGTERM 信号
```

### 重启服务
```bash
# 停止后重新启动
./stop.sh && ./start.sh
```

### 检查服务状态
```bash
# 健康检查
curl http://localhost:8002/api/health

# 或在浏览器中访问
# http://localhost:8002/api/health
```

### 查看服务日志
```bash
# 服务会在控制台输出日志
# 生产环境建议重定向到文件：
python main.py > logs/service.log 2>&1
```

## 🏃‍♂️ 开发者1分钟快速启动

```bash
# 1. 进入目录
cd python-excel-service

# 2. 一键启动（自动安装依赖）
./start.sh
```

完成！服务现在运行在 http://localhost:8002

**或者传统方式：**
```bash
# 1. 手动安装依赖
pip install -r requirements.txt

# 2. 启动服务
python main.py
```

## 📁 项目结构

```
python-excel-service/
├── main.py                                    # FastAPI主服务 ⭐
├── start.sh                                   # 一键启动脚本 ⭐
├── stop.sh                                    # 一键停止脚本 ⭐
├── excel_aspose_processor.py                  # Aspose转换处理器
├── pdf_watermark_remover_advanced_delete.py   # 真删除水印器 ⭐
├── requirements.txt                           # Python依赖列表
├── scripts/                                   # 部署脚本目录
│   ├── deploy.sh                             # 一键部署脚本
│   ├── docker-deploy.sh                      # Docker部署
│   ├── server-deploy.sh                      # 云服务器部署
│   ├── railway-deploy.sh                     # Railway云平台
│   └── render-deploy.sh                      # Render云平台
├── logs/                                      # 日志目录（运行时创建）
├── uploads/                                   # 上传文件临时目录
├── outputs/                                   # 输出文件临时目录
├── README.md                                  # 项目说明
├── DEPLOYMENT.md                              # 详细部署指南
└── .gitignore                                # Git忽略文件配置
```

### 核心文件说明

- **main.py**: 简化版FastAPI服务，专注Aspose转换+水印删除
- **start.sh**: 一键启动脚本，自动环境检查、依赖安装、服务检测
- **stop.sh**: 一键停止脚本，优雅停止服务
- **excel_aspose_processor.py**: Aspose.Cells转换器，支持多种Excel格式
- **pdf_watermark_remover_advanced_delete.py**: 使用PyMuPDF真正删除水印
- **scripts/deploy.sh**: 一键选择部署方案的主脚本

## 🎯 性能优势

相比复杂版本的改进：
- 移除LibreOffice依赖，减少系统复杂度
- 专注Aspose.Cells单一引擎，提升稳定性  
- 默认启用真删除水印，简化使用流程
- API接口精简，更易集成使用