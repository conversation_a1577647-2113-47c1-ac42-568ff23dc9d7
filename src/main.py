"""
Excel转PDF服务 - Cloudflare Workers版本
"""

import json
from datetime import datetime

async def on_fetch(request, env):
    """Cloudflare Workers入口函数"""

    try:
        # 获取请求信息
        url = str(request.url)
        method = request.method

        # 解析路径 - 更简单的方式
        if "health" in url:
            path = "health"
        elif url.endswith("/") or url.endswith("main.py"):
            path = ""
        else:
            path = "unknown"

        # 设置CORS头
        cors_headers = {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type",
            "Content-Type": "application/json"
        }

        # 处理OPTIONS请求（CORS预检）
        if method == "OPTIONS":
            from js import Response
            return Response.new("", {"headers": cors_headers})

        # 健康检查
        if method == "GET" and path == "health":
            response_data = {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "platform": "cloudflare_workers",
                "message": "Excel to PDF Service",
                "service_type": "excel_to_pdf"
            }
            from js import Response
            return Response.new(
                json.dumps(response_data),
                {"headers": cors_headers}
            )

        # 根路径
        elif method == "GET":
            response_data = {
                "message": "Excel to PDF Service on Cloudflare Workers!",
                "status": "running",
                "timestamp": datetime.now().isoformat()
            }
            from js import Response
            return Response.new(
                json.dumps(response_data),
                {"headers": cors_headers}
            )

        # 其他请求 - 返回404
        else:
            error_response = {
                "error": "Not Found",
                "method": method,
                "url": url,
                "timestamp": datetime.now().isoformat()
            }
            from js import Response
            return Response.new(
                json.dumps(error_response),
                {"status": 404, "headers": cors_headers}
            )

    except Exception as e:
        # 全局错误处理
        error_response = {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
        from js import Response
        return Response.new(
            json.dumps(error_response),
            {"status": 500, "headers": {
                "Access-Control-Allow-Origin": "*",
                "Content-Type": "application/json"
            }}
        )
