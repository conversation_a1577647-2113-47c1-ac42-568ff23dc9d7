"""
Excel转PDF服务 - Cloudflare Workers版本
由于Cloudflare Workers的限制，这是一个简化版本
"""

import json
import base64
from datetime import datetime
import io
import tempfile
import os

# 由于Cloudflare Workers限制，我们需要使用标准库版本
# 在本地开发时可以使用FastAPI，生产环境使用Workers API

try:
    # 本地开发环境 - 使用FastAPI
    from fastapi import FastAPI, UploadFile, File, HTTPException, Request
    from fastapi.responses import J<PERSON>NResponse, Response
    from fastapi.middleware.cors import CORSMiddleware
    import aiofiles
    
    # 创建FastAPI应用（仅用于本地开发）
    app = FastAPI(
        title="Excel to PDF Service",
        description="Excel转PDF转换服务 - Cloudflare Workers版本",
        version="3.0.0"
    )
    
    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    @app.get("/")
    async def root():
        return {"message": "Excel to PDF Service on Cloudflare Workers!", "status": "running"}
    
    @app.get("/api/health")
    async def health_check(request: Request):
        env = getattr(request.scope, "env", {})
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "platform": "cloudflare_workers",
            "message": env.get("MESSAGE", "Service running"),
            "service_type": "excel_to_pdf"
        }
    
    @app.post("/api/excel-to-pdf/convert")
    async def convert_excel_fastapi(file: UploadFile = File(...)):
        """FastAPI版本的转换接口（仅本地开发）"""
        try:
            # 验证文件类型
            allowed_extensions = {'.xlsx', '.xls', '.xlsb', '.xlsm'}
            file_ext = os.path.splitext(file.filename.lower())[1]
            if file_ext not in allowed_extensions:
                raise HTTPException(
                    status_code=400, 
                    detail=f"不支持的文件类型: {file_ext}"
                )
            
            # 读取文件内容
            content = await file.read()
            
            # 模拟转换过程（实际生产环境需要真实的转换逻辑）
            result = {
                "success": True,
                "message": "文件上传成功，转换功能在生产环境中实现",
                "filename": file.filename,
                "size": len(content),
                "timestamp": datetime.now().isoformat(),
                "note": "这是本地开发版本，生产环境请使用Workers API"
            }
            
            return result
            
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"处理异常: {str(e)}")
    
    # FastAPI的ASGI适配器
    async def on_fetch(request, env):
        """Cloudflare Workers入口函数"""
        import asgi
        return await asgi.fetch(app, request, env)

except ImportError:
    # 生产环境 - 仅使用标准库
    app = None

# Cloudflare Workers生产环境处理函数
async def handle_request(request, env):
    """处理Cloudflare Workers请求的主函数"""
    
    # 解析请求
    url = request.url
    method = request.method
    path = url.split('/')[-1] if '/' in url else ''
    
    # 设置CORS头
    cors_headers = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
        "Content-Type": "application/json"
    }
    
    # 处理OPTIONS请求（CORS预检）
    if method == "OPTIONS":
        return Response("", headers=cors_headers)
    
    # 根路径
    if method == "GET" and (path == "" or path == "main.py"):
        response_data = {
            "message": "Excel to PDF Service on Cloudflare Workers!",
            "status": "running",
            "timestamp": datetime.now().isoformat()
        }
        return Response(
            json.dumps(response_data),
            headers=cors_headers
        )
    
    # 健康检查
    elif method == "GET" and path == "health":
        response_data = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "platform": "cloudflare_workers",
            "message": env.get("MESSAGE", "Service running"),
            "service_type": "excel_to_pdf",
            "max_file_size": env.get("MAX_FILE_SIZE", "10485760"),
            "allowed_extensions": env.get("ALLOWED_EXTENSIONS", ".xlsx,.xls,.xlsb,.xlsm")
        }
        return Response(
            json.dumps(response_data),
            headers=cors_headers
        )
    
    # 文件转换接口
    elif method == "POST" and "convert" in path:
        try:
            # 获取请求体
            body = await request.text()
            
            # 在实际生产环境中，这里需要：
            # 1. 解析multipart/form-data
            # 2. 验证文件类型
            # 3. 调用Excel转PDF转换服务
            # 4. 返回转换结果
            
            response_data = {
                "success": False,
                "message": "Excel转PDF转换功能需要外部依赖，当前Cloudflare Workers不支持",
                "note": "请考虑使用Cloudflare Containers或其他支持完整Python包的平台",
                "timestamp": datetime.now().isoformat(),
                "alternatives": [
                    "使用Cloudflare Containers",
                    "部署到Vercel/Heroku/AWS Lambda",
                    "等待Cloudflare Workers Python支持成熟"
                ]
            }
            
            return Response(
                json.dumps(response_data),
                status=501,  # Not Implemented
                headers=cors_headers
            )
            
        except Exception as e:
            error_response = {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
            return Response(
                json.dumps(error_response),
                status=500,
                headers=cors_headers
            )
    
    # 404 - 未找到
    else:
        error_response = {
            "error": "Not Found",
            "path": path,
            "method": method,
            "timestamp": datetime.now().isoformat()
        }
        return Response(
            json.dumps(error_response),
            status=404,
            headers=cors_headers
        )

# Cloudflare Workers入口函数
async def on_fetch(request, env):
    """Cloudflare Workers的主入口函数"""
    if app is not None:
        # 本地开发环境，使用FastAPI
        import asgi
        return await asgi.fetch(app, request, env)
    else:
        # 生产环境，使用标准库处理
        return await handle_request(request, env)
