# Excel转PDF服务 - 部署指南

本服务为 [7788.tools](https://7788.tools) 提供高质量的Excel转PDF转换功能，支持真正删除Aspose评估版水印。

## 🚀 一键部署

运行主部署脚本，选择适合的部署方案：

```bash
./scripts/deploy.sh
```

## 📋 部署方案对比

| 特性 | Docker本地 | 云服务器 | Railway | Render |
|------|------------|----------|---------|---------|
| 部署难度 | 简单 | 中等 | 很简单 | 简单 |
| 成本 | 免费 | $5-20/月 | 免费/付费 | 免费/付费 |
| 性能 | 高 | 最高 | 中等 | 高 |
| 扩展性 | 手动 | 手动 | 自动 | 自动 |
| SSL证书 | 需配置 | 自动 | 自动 | 自动 |
| 自定义域名 | 需配置 | 支持 | 支持 | 支持 |
| 文件限制 | 无限制 | 无限制 | 100MB | 100MB |
| 休眠机制 | 无 | 无 | 有 | 有(免费版) |

## 🐳 Docker 本地部署

适合开发测试环境，快速启动。

### 要求
- Docker已安装
- 8002端口可用

### 部署命令
```bash
# 生产环境
./scripts/docker-deploy.sh production

# 开发环境
./scripts/docker-deploy.sh development
```

### 管理命令
```bash
# 查看容器状态
docker ps -f name=excel-pdf-service-container

# 查看日志
docker logs excel-pdf-service-container

# 停止服务
docker stop excel-pdf-service-container

# 重启服务
docker restart excel-pdf-service-container
```

## 🌐 云服务器部署

适合生产环境，完整配置，支持高并发。

### 要求
- Ubuntu 18.04+ / CentOS 7+ / Debian 9+
- 至少1GB内存
- sudo权限

### 部署命令
```bash
./scripts/server-deploy.sh
```

### 配置域名
1. 将 `api.7788.tools` 解析到服务器IP
2. 配置SSL证书：
   ```bash
   sudo certbot --nginx -d api.7788.tools
   ```

### 管理命令
```bash
# 服务管理
/opt/excel-pdf-service/manage-service.sh {start|stop|restart|status|logs|health}

# 查看进程状态
sudo supervisorctl status excel-pdf-service

# 查看日志
tail -f /opt/excel-pdf-service/logs/service.log
```

## 🚄 Railway 云平台

适合快速原型和小型项目，零配置部署。

### 要求
- GitHub账号
- Railway账号

### 部署步骤
```bash
./scripts/railway-deploy.sh
```

### 特点
- 自动从Git部署
- 免费额度：512MB内存，100GB带宽/月
- 自动SSL和域名
- 无活动时休眠（可升级避免）

## 🎨 Render 云平台

适合生产级应用，稳定可靠。

### 要求
- GitHub账号
- Render账号

### 部署步骤
```bash
./scripts/render-deploy.sh
```

### 手动配置
1. 在 [Render.com](https://render.com) 创建Web Service
2. 连接GitHub仓库
3. 配置：
   - Build Command: `pip install -r requirements.txt`
   - Start Command: `python main.py`
   - Environment: Python
   - Health Check Path: `/api/health`

### 特点
- 免费计划：512MB内存，100GB带宽/月
- 自动SSL、CDN、备份
- 15分钟无活动休眠（付费版不休眠）
- 支持自定义域名

## 🔧 环境配置

### API地址配置

根据部署方案，前端需要配置对应的API地址：

```typescript
// 前端配置文件
const API_BASE_URL = {
  // Docker本地部署
  local: 'http://localhost:8002',
  
  // 云服务器部署
  production: 'https://api.7788.tools',
  
  // Railway部署
  railway: 'https://excel-pdf-service.railway.app',
  
  // Render部署
  render: 'https://excel-pdf-service.onrender.com'
}
```

### 环境变量

```bash
# 服务配置
HOST=0.0.0.0
PORT=8002
ENV=production

# Python配置
PYTHONUNBUFFERED=1

# CORS配置
ALLOWED_ORIGINS=https://7788.tools,http://localhost:3000
```

## 🔒 安全配置

### CORS设置
服务已配置CORS支持，允许来自 `https://7788.tools` 的请求。

### SSL证书
- 云服务器：自动通过certbot配置Let's Encrypt
- Railway/Render：自动提供SSL
- Docker本地：需要自行配置

### 防火墙
云服务器部署会自动配置防火墙规则：
- 22端口：SSH访问
- 80端口：HTTP（重定向到HTTPS）
- 443端口：HTTPS

## 📊 监控与日志

### 健康检查
所有部署方案都支持健康检查：
```bash
curl https://your-api-domain/api/health
```

### 日志查看
- Docker: `docker logs excel-pdf-service-container`
- 云服务器: `tail -f /opt/excel-pdf-service/logs/service.log`
- Railway: `railway logs`
- Render: 在控制台查看

### 性能监控
建议配置：
- Uptime监控（如UptimeRobot）
- 错误报告（如Sentry）
- 性能分析（如New Relic）

## 🛠 故障排除

### 常见问题

1. **服务无法启动**
   - 检查端口占用：`lsof -i :8002`
   - 查看错误日志
   - 确认Python依赖完整

2. **文件上传失败**
   - 检查文件大小限制
   - 确认uploads目录权限
   - 查看磁盘空间

3. **CORS错误**
   - 确认前端域名在ALLOWED_ORIGINS中
   - 检查Nginx配置
   - 查看浏览器网络面板

### 联系支持
如遇问题，请查看：
- 服务日志
- GitHub Issues
- 部署平台的文档和支持

## 📈 扩展部署

### 负载均衡
生产环境可配置多实例+负载均衡：

```nginx
upstream excel_pdf_backend {
    server 127.0.0.1:8002;
    server 127.0.0.1:8003;
    server 127.0.0.1:8004;
}
```

### 数据库
如需持久化存储，可添加：
- PostgreSQL：存储转换记录
- Redis：缓存和会话管理
- MinIO/S3：文件存储

### 容器编排
大规模部署可使用：
- Docker Swarm
- Kubernetes
- AWS ECS/EKS

---

## 🎯 推荐部署路径

1. **开发阶段**: Docker本地部署
2. **测试验证**: Railway快速部署
3. **生产环境**: 
   - 小流量：Render付费版
   - 大流量：云服务器集群

选择最适合你需求和预算的方案！🚀