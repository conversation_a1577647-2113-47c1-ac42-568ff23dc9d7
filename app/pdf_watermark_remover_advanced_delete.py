#!/usr/bin/env python3
"""
高级PDF水印删除 - 通过redact功能真正删除文字
"""

import os
import logging
from typing import Tuple, List

try:
    import fitz  # PyMuPDF
    FITZ_AVAILABLE = True
except ImportError:
    FITZ_AVAILABLE = False

logger = logging.getLogger(__name__)

class AdvancedWatermarkRemover:
    """使用PyMuPDF的redact功能真正删除水印"""
    
    def __init__(self):
        self.available = FITZ_AVAILABLE
        if not self.available:
            logger.warning("高级删除功能不可用，请安装PyMuPDF")
        else:
            logger.info("高级水印删除器可用")
    
    def remove_watermark_with_redaction(
        self,
        input_pdf: str,
        output_pdf: str,
        watermark_texts: List[str] = None
    ) -> Tuple[bool, str]:
        """
        使用redact功能真正删除水印文字
        redact是PyMuPDF提供的"编辑删除"功能，会真正移除内容
        """
        if watermark_texts is None:
            watermark_texts = [
                "Evaluation Only. Created with Aspose.Cells for Python via .NET. Copyright 2003 - 2025 Aspose Pty Ltd.",
                "Evaluation Only. Created with Aspose.Cells for Python via .NET.",
                "Created with Aspose.Cells for Python via .NET.",
                "Evaluation Only",
                "Aspose.Cells"
            ]
        
        try:
            doc = fitz.open(input_pdf)
            logger.info(f"使用redact功能处理PDF: {input_pdf}, 页数: {doc.page_count}")
            
            total_redacted = 0
            
            for page_num in range(doc.page_count):
                page = doc.load_page(page_num)
                redacted = 0
                
                # 使用search_for查找所有水印文字实例
                for watermark_text in watermark_texts:
                    # 搜索完整水印文字
                    text_instances = page.search_for(watermark_text)
                    
                    for inst in text_instances:
                        # 标记为redact（删除）
                        annot = page.add_redact_annot(inst)
                        redacted += 1
                        logger.info(f"第{page_num+1}页：标记删除文字区域 '{watermark_text}' at {inst}")
                
                # 执行redaction操作
                if redacted > 0:
                    page.apply_redactions()
                    logger.info(f"第{page_num+1}页：已执行删除操作，删除了{redacted}个文字区域")
                    total_redacted += redacted
            
            # 保存处理后的PDF
            doc.save(output_pdf)
            doc.close()
            
            file_size = os.path.getsize(output_pdf)
            
            if total_redacted > 0:
                message = f"Redact删除成功，共删除{total_redacted}个水印区域，文件大小: {file_size} bytes"
                logger.info(message)
                return True, message
            else:
                message = f"未找到匹配的水印文字，文件大小: {file_size} bytes"
                logger.info(message)
                return True, message
                
        except Exception as e:
            error_msg = f"Redact删除失败: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def remove_watermark_with_selective_redaction(
        self,
        input_pdf: str,
        output_pdf: str,
        watermark_texts: List[str] = None
    ) -> Tuple[bool, str]:
        """
        选择性redact删除 - 更精确的匹配
        """
        if watermark_texts is None:
            watermark_texts = [
                "Evaluation Only. Created with Aspose.Cells for Python via .NET. Copyright 2003 - 2025 Aspose Pty Ltd.",
                "Evaluation Only. Created with Aspose.Cells for Python via .NET.",
                "Created with Aspose.Cells for Python via .NET.",
                "Evaluation Only",
                "Aspose.Cells"
            ]
        
        try:
            doc = fitz.open(input_pdf)
            logger.info(f"使用选择性redact处理PDF: {input_pdf}, 页数: {doc.page_count}")
            
            total_redacted = 0
            
            for page_num in range(doc.page_count):
                page = doc.load_page(page_num)
                
                # 获取页面文本字典
                text_dict = page.get_text("dict")
                
                # 遍历所有文本块，精确匹配水印
                for block in text_dict["blocks"]:
                    if "lines" in block:
                        for line in block["lines"]:
                            for span in line["spans"]:
                                span_text = span["text"].strip()
                                
                                # 检查是否匹配水印
                                for watermark_text in watermark_texts:
                                    if self._is_watermark_match(span_text, watermark_text):
                                        logger.info(f"第{page_num+1}页：发现水印文字 '{span_text}'")
                                        
                                        # 获取精确的边界框
                                        bbox = span["bbox"]
                                        rect = fitz.Rect(bbox)
                                        
                                        # 添加redaction注释
                                        redact_annot = page.add_redact_annot(rect)
                                        # 设置删除后的填充色（可选）
                                        redact_annot.set_colors({"fill": [1, 1, 1]})  # 白色填充
                                        redact_annot.update()
                                        
                                        total_redacted += 1
                                        logger.debug(f"第{page_num+1}页：添加redact注释 {rect}")
                                        break
                
                # 执行页面的所有redaction
                if page.annot_names():  # 如果有注释
                    page.apply_redactions()
                    logger.info(f"第{page_num+1}页：执行redaction操作")
            
            # 保存处理后的PDF
            doc.save(output_pdf)
            doc.close()
            
            file_size = os.path.getsize(output_pdf)
            
            if total_redacted > 0:
                message = f"选择性删除成功，共删除{total_redacted}个水印，文件大小: {file_size} bytes"
                logger.info(message)
                return True, message
            else:
                message = f"未找到匹配的水印文字，文件大小: {file_size} bytes"
                logger.info(message)
                return True, message
                
        except Exception as e:
            error_msg = f"选择性删除失败: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def _is_watermark_match(self, text: str, watermark: str) -> bool:
        """判断是否匹配水印文字"""
        if not text or not watermark:
            return False
        
        # 完全匹配
        if text == watermark:
            return True
        
        # 包含匹配
        if watermark in text or text in watermark:
            return True
        
        return False

def remove_watermark_truly_delete(
    input_pdf: str,
    output_pdf: str,
    watermark_texts: List[str] = None,
    method: str = "redaction"  # "redaction" 或 "selective"
) -> Tuple[bool, str]:
    """
    真正删除PDF水印的函数
    
    Args:
        method: "redaction" 使用搜索+redact, "selective" 使用精确匹配+redact
    """
    remover = AdvancedWatermarkRemover()
    
    if not remover.available:
        return False, "高级删除功能不可用"
    
    if method == "selective":
        return remover.remove_watermark_with_selective_redaction(
            input_pdf, output_pdf, watermark_texts
        )
    else:
        return remover.remove_watermark_with_redaction(
            input_pdf, output_pdf, watermark_texts
        )

# 测试代码
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    if FITZ_AVAILABLE:
        test_input = "test_with_watermark.pdf"
        
        # 测试两种方法
        methods = [
            ("redaction", "搜索+Redact删除"),
            ("selective", "精确匹配+Redact删除")
        ]
        
        for method, desc in methods:
            test_output = f"test_redact_{method}.pdf"
            
            print(f"\n🔬 测试 {desc}")
            print("=" * 50)
            
            if os.path.exists(test_input):
                success, message = remove_watermark_truly_delete(
                    test_input, test_output, method=method
                )
                print(f"结果: {message}")
                
                # 验证效果
                if success and os.path.exists(test_output):
                    print("🔍 验证删除效果...")
                    
                    # 分析文字内容
                    try:
                        doc_orig = fitz.open(test_input)
                        doc_new = fitz.open(test_output)
                        
                        orig_text = ""
                        new_text = ""
                        
                        for page_num in range(doc_orig.page_count):
                            orig_text += doc_orig.load_page(page_num).get_text()
                            new_text += doc_new.load_page(page_num).get_text()
                        
                        doc_orig.close()
                        doc_new.close()
                        
                        # 检查Aspose水印
                        aspose_in_orig = "Aspose.Cells" in orig_text
                        aspose_in_new = "Aspose.Cells" in new_text
                        evaluation_in_orig = "Evaluation Only" in orig_text
                        evaluation_in_new = "Evaluation Only" in new_text
                        
                        print(f"  原文件Aspose.Cells: {'✓' if aspose_in_orig else '✗'}")
                        print(f"  处理后Aspose.Cells: {'✓' if aspose_in_new else '✗'}")
                        print(f"  原文件Evaluation Only: {'✓' if evaluation_in_orig else '✗'}")
                        print(f"  处理后Evaluation Only: {'✓' if evaluation_in_new else '✗'}")
                        
                        if (aspose_in_orig and not aspose_in_new) or (evaluation_in_orig and not evaluation_in_new):
                            print("  🎉 水印已真正删除！")
                        elif aspose_in_orig and aspose_in_new:
                            print("  ⚠️  水印仍然存在")
                        else:
                            print("  ℹ️  无明显变化")
                            
                        # 字符统计
                        print(f"  原文件字符数: {len(orig_text)}")
                        print(f"  处理后字符数: {len(new_text)}")
                        print(f"  字符差异: {len(new_text) - len(orig_text):+d}")
                        
                    except Exception as e:
                        print(f"  ❌ 验证失败: {e}")
                        
            else:
                print(f"❌ 测试文件不存在: {test_input}")
                
    else:
        print("❌ PyMuPDF未安装，无法测试")