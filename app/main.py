"""
Excel转PDF服务 - 简化版（仅支持Aspose + 真正删除水印）
"""

from fastapi import FastAPI, UploadFile, File, HTTPException, Query
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import os
import uuid
import aiofiles
import sys
from datetime import datetime
import logging

# 导入Excel处理器
from excel_aspose_processor import convert_excel_to_pdf_aspose, is_aspose_available

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 检查Aspose.Cells可用性
aspose_available = is_aspose_available()

if not aspose_available:
    logger.error("Aspose.Cells不可用，请安装: pip install aspose-cells-python")
    sys.exit(1)
else:
    logger.info("Aspose.Cells转换器可用，支持真正删除水印")

# 创建FastAPI应用
app = FastAPI(
    title="Excel to PDF Service",
    description="专业Excel转PDF转换服务，支持真正删除水印",
    version="2.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置目录
UPLOAD_DIR = "uploads"
OUTPUT_DIR = "outputs"

# 确保目录存在
os.makedirs(UPLOAD_DIR, exist_ok=True)
os.makedirs(OUTPUT_DIR, exist_ok=True)

@app.get("/api/health")
def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "platform": sys.platform,
        "aspose_available": aspose_available,
        "watermark_removal": "enabled",
        "upload_dir": os.path.exists(UPLOAD_DIR),
        "output_dir": os.path.exists(OUTPUT_DIR),
        "service_type": "aspose_only"
    }

@app.post("/api/excel-to-pdf/convert")
async def convert_excel_direct(
    file: UploadFile = File(...),
    remove_watermark: bool = Query(True, description="是否移除水印")
):
    """
    直接转换Excel到PDF（推荐API）
    默认启用真正删除水印功能
    """
    
    try:
        logger.info(f"收到转换请求 - remove_watermark: {remove_watermark}")
        
        # 验证文件类型
        allowed_extensions = {'.xlsx', '.xls', '.xlsb', '.xlsm', '.csv'}
        file_ext = os.path.splitext(file.filename.lower())[1]
        if file_ext not in allowed_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的文件类型: {file_ext}，支持的类型: {', '.join(allowed_extensions)}"
            )
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = os.path.splitext(file.filename)[0]
        upload_filename = f"{uuid.uuid4().hex[:8]}_{file.filename}"
        upload_path = os.path.join(UPLOAD_DIR, upload_filename)
        pdf_filename = f"{base_name}_aspose_{timestamp}.pdf"
        pdf_path = os.path.join(OUTPUT_DIR, pdf_filename)
        
        # 保存上传文件
        async with aiofiles.open(upload_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        logger.info(f"文件上传成功: {file.filename} -> {upload_path}")
        
        # Aspose转换
        logger.info(f"Aspose转换参数 - remove_watermark: {remove_watermark}")
        
        start_time = datetime.now()
        success, message = convert_excel_to_pdf_aspose(
            upload_path,
            pdf_path,
            {
                'remove_watermark': remove_watermark,
                'watermark_method': "text_detection"  # 固定使用真正删除方法
            }
        )
        end_time = datetime.now()
        conversion_time = (end_time - start_time).total_seconds()
        
        # 清理上传文件
        try:
            os.remove(upload_path)
        except:
            pass
        
        if success:
            file_size = os.path.getsize(pdf_path) if os.path.exists(pdf_path) else 0
            logger.info(f"Aspose转换成功: {pdf_path}, 大小: {file_size} bytes, 耗时: {conversion_time:.2f}s")
            
            return {
                "success": True,
                "message": message,
                "pdf_path": pdf_filename,
                "file_size": file_size,
                "conversion_time": round(conversion_time, 2),
                "method_used": "aspose_cells",
                "watermark_removed": remove_watermark,
                "download_url": f"/api/aspose-excel-to-pdf/download/{pdf_filename}"
            }
        else:
            logger.error(f"Aspose转换失败: {message}")
            raise HTTPException(status_code=500, detail=f"转换失败: {message}")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"转换过程发生异常: {e}")
        # 清理文件
        try:
            if 'upload_path' in locals() and os.path.exists(upload_path):
                os.remove(upload_path)
        except:
            pass
        raise HTTPException(status_code=500, detail=f"转换异常: {str(e)}")

@app.get("/api/aspose-excel-to-pdf/download/{filename}")
def download_pdf(filename: str):
    """下载转换后的PDF文件"""
    import urllib.parse
    
    file_path = os.path.join(OUTPUT_DIR, filename)
    
    if not os.path.exists(file_path):
        logger.warning(f"下载文件不存在: {filename}")
        raise HTTPException(status_code=404, detail="文件不存在或已过期")
    
    logger.info(f"下载文件: {filename}")
    
    # 对文件名进行URL编码以处理中文字符
    encoded_filename = urllib.parse.quote(filename)
    
    return FileResponse(
        file_path,
        media_type='application/pdf',
        filename=filename,
        headers={
            "Content-Disposition": f"attachment; filename*=UTF-8''{encoded_filename}",
            "Cache-Control": "no-cache",
        }
    )

# 兼容性API - 重定向到新API
@app.post("/api/excel-to-pdf/convert-smart")
async def convert_smart_legacy(file: UploadFile = File(...)):
    """兼容性API：智能转换（重定向到新API）"""
    return await convert_excel_direct(file, remove_watermark=True)

@app.post("/api/aspose-excel-to-pdf/convert")
async def convert_aspose_legacy(file: UploadFile = File(...)):
    """兼容性API：Aspose转换（重定向到新API）"""
    return await convert_excel_direct(file, remove_watermark=True)

# 启动和关闭事件
@app.on_event("startup")
async def startup_event():
    logger.info("Excel转PDF服务启动")
    logger.info(f"平台: {sys.platform}")
    logger.info(f"Aspose.Cells可用: {aspose_available}")
    logger.info("真正删除水印功能: 已启用")

@app.on_event("shutdown") 
async def shutdown_event():
    logger.info("Excel转PDF服务关闭")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)