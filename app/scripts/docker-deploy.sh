#!/bin/bash

# Excel转PDF Python服务 Docker部署脚本
# 用法: ./scripts/docker-deploy.sh [production|development]

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色输出
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
ENVIRONMENT=${1:-production}
if [[ "$ENVIRONMENT" != "production" && "$ENVIRONMENT" != "development" ]]; then
    print_error "无效的环境参数，支持: production 或 development"
    exit 1
fi

print_info "开始部署Excel转PDF服务 - 环境: $ENVIRONMENT"

# 配置变量
APP_NAME="excel-pdf-service"
IMAGE_NAME="$APP_NAME:latest"
CONTAINER_NAME="$APP_NAME-container"
HOST_PORT="8002"
CONTAINER_PORT="8002"

# 创建Dockerfile
print_info "创建Dockerfile..."
cat > Dockerfile << 'EOF'
# Excel转PDF服务 Dockerfile
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p uploads outputs logs

# 暴露端口
EXPOSE 8002

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8002/api/health || exit 1

# 启动命令
CMD ["python", "main.py"]
EOF

print_success "Dockerfile创建完成"

# 创建.dockerignore
print_info "创建.dockerignore..."
cat > .dockerignore << 'EOF'
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
venv/
.venv/
ENV/
env/

# 测试和缓存
.pytest_cache/
.coverage
htmlcov/
.tox/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 日志和临时文件
logs/
*.log
uploads/
outputs/
*.tmp
*.temp

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# 脚本
scripts/

# 文档
README.md
docs/
*.md

# 测试文件
test*
*test*
EOF

print_success ".dockerignore创建完成"

# 停止并删除现有容器
print_info "清理现有容器..."
if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
    print_info "停止现有容器..."
    docker stop "$CONTAINER_NAME"
fi

if docker ps -aq -f name="$CONTAINER_NAME" | grep -q .; then
    print_info "删除现有容器..."
    docker rm "$CONTAINER_NAME"
fi

# 删除现有镜像
if docker images -q "$IMAGE_NAME" | grep -q .; then
    print_info "删除现有镜像..."
    docker rmi "$IMAGE_NAME"
fi

# 构建Docker镜像
print_info "构建Docker镜像..."
docker build -t "$IMAGE_NAME" .

print_success "Docker镜像构建完成"

# 设置环境特定的参数
if [[ "$ENVIRONMENT" == "production" ]]; then
    RESTART_POLICY="--restart=always"
    NETWORK_MODE=""
    VOLUME_MOUNTS="-v $(pwd)/logs:/app/logs -v $(pwd)/uploads:/app/uploads -v $(pwd)/outputs:/app/outputs"
    ENV_VARS="-e PYTHONUNBUFFERED=1 -e ENV=production"
else
    RESTART_POLICY=""
    NETWORK_MODE=""
    VOLUME_MOUNTS="-v $(pwd):/app"
    ENV_VARS="-e PYTHONUNBUFFERED=1 -e ENV=development"
fi

# 运行容器
print_info "启动Docker容器..."
docker run -d \
    --name "$CONTAINER_NAME" \
    -p "$HOST_PORT:$CONTAINER_PORT" \
    $RESTART_POLICY \
    $VOLUME_MOUNTS \
    $ENV_VARS \
    "$IMAGE_NAME"

print_success "容器启动完成"

# 等待服务启动
print_info "等待服务启动..."
sleep 5

# 检查容器状态
if docker ps -f name="$CONTAINER_NAME" | grep -q "$CONTAINER_NAME"; then
    print_success "容器运行正常"
    
    # 健康检查
    print_info "执行健康检查..."
    for i in {1..10}; do
        if curl -s -f "http://localhost:$HOST_PORT/api/health" > /dev/null; then
            print_success "服务健康检查通过"
            break
        fi
        if [ $i -eq 10 ]; then
            print_warning "健康检查超时，但容器正在运行"
        fi
        print_info "等待服务启动... ($i/10)"
        sleep 2
    done
    
    # 显示服务信息
    echo ""
    print_success "Excel转PDF服务部署成功！"
    echo ""
    echo "服务信息:"
    echo "  - 服务地址: http://localhost:$HOST_PORT"
    echo "  - 健康检查: http://localhost:$HOST_PORT/api/health" 
    echo "  - API文档: http://localhost:$HOST_PORT/docs"
    echo "  - 容器名称: $CONTAINER_NAME"
    echo "  - 镜像名称: $IMAGE_NAME"
    echo ""
    echo "常用管理命令:"
    echo "  - 查看容器状态: docker ps -f name=$CONTAINER_NAME"
    echo "  - 查看容器日志: docker logs $CONTAINER_NAME"
    echo "  - 停止服务: docker stop $CONTAINER_NAME"
    echo "  - 重启服务: docker restart $CONTAINER_NAME"
    echo ""
    
else
    print_error "容器启动失败"
    print_info "查看容器日志:"
    docker logs "$CONTAINER_NAME"
    exit 1
fi

print_success "部署完成！"