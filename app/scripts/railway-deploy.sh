#!/bin/bash

# Excel转PDF Python服务 Railway部署脚本
# Railway.app是一个现代化的云平台，支持Python服务
# 用法: ./scripts/railway-deploy.sh

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info "准备部署Excel转PDF服务到Railway..."

# 检查Railway CLI
if ! command -v railway &> /dev/null; then
    print_warning "Railway CLI未安装，正在安装..."
    if command -v npm &> /dev/null; then
        npm install -g @railway/cli
    else
        print_error "请先安装Node.js或手动安装Railway CLI"
        print_info "安装方法: curl -fsSL https://railway.app/install.sh | sh"
        exit 1
    fi
fi

# 创建railway.json配置文件
print_info "创建Railway配置文件..."
cat > railway.json << 'EOF'
{
  "$schema": "https://railway.app/railway.schema.json",
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "numReplicas": 1,
    "sleepApplication": false,
    "restartPolicyType": "ON_FAILURE"
  }
}
EOF

# 创建Procfile（如果需要）
print_info "创建Procfile..."
cat > Procfile << 'EOF'
web: python main.py
EOF

# 创建runtime.txt指定Python版本
print_info "创建runtime.txt..."
cat > runtime.txt << 'EOF'
python-3.11.8
EOF

# 创建.railwayignore
print_info "创建.railwayignore..."
cat > .railwayignore << 'EOF'
# 开发文件
.venv/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
pip-log.txt
pip-delete-this-directory.txt

# 测试文件
.pytest_cache/
.coverage
htmlcov/
.tox/

# 临时文件
logs/
uploads/
outputs/
*.log
*.tmp

# IDE
.vscode/
.idea/
*.swp
*.swo

# Git
.git/
.gitignore

# 脚本
scripts/

# 文档
README.md
docs/
*.md
EOF

# 登录Railway
print_info "登录Railway..."
railway login

# 初始化项目
print_info "初始化Railway项目..."
if [ ! -f ".railway/config.json" ]; then
    railway init excel-pdf-service
fi

# 链接到项目
railway link

# 设置环境变量
print_info "设置环境变量..."
railway variables set PYTHONUNBUFFERED=1
railway variables set PORT=8002
railway variables set HOST=0.0.0.0

# 部署
print_info "开始部署..."
railway deploy

# 获取部署URL
DEPLOY_URL=$(railway status --json | python3 -c "import sys, json; print(json.load(sys.stdin).get('deployments', [{}])[0].get('url', 'N/A'))" 2>/dev/null || echo "请查看Railway控制台获取URL")

print_success "部署完成！"
echo ""
echo "部署信息:"
echo "  - Railway项目: excel-pdf-service"
echo "  - 部署URL: $DEPLOY_URL"
echo "  - 健康检查: $DEPLOY_URL/api/health"
echo "  - API文档: $DEPLOY_URL/docs"
echo ""
echo "管理命令:"
echo "  - 查看状态: railway status"
echo "  - 查看日志: railway logs"
echo "  - 重新部署: railway deploy"
echo "  - 查看变量: railway variables"
echo ""
echo "配置前端:"
echo "  - 更新前端API地址为: $DEPLOY_URL"
echo "  - 或配置自定义域名后使用HTTPS"
echo ""

print_warning "注意事项:"
echo "  - Railway免费计划有使用限制"
echo "  - 生产环境建议升级到付费计划"
echo "  - 可以在Railway控制台配置自定义域名"