#!/bin/bash

# Excel转PDF Python服务 Render部署配置脚本
# Render.com是一个支持Python的现代化云平台
# 用法: ./scripts/render-deploy.sh

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info "准备Render部署配置..."

# 创建render.yaml配置文件
print_info "创建render.yaml配置文件..."
cat > render.yaml << 'EOF'
services:
  - type: web
    name: excel-pdf-service
    env: python
    plan: free  # 可改为 starter, standard, pro
    buildCommand: pip install -r requirements.txt
    startCommand: python main.py
    envVars:
      - key: PYTHONUNBUFFERED
        value: "1"
      - key: PORT
        value: "8002"
      - key: HOST
        value: "0.0.0.0"
    healthCheckPath: /api/health
    autoDeploy: true
    branch: main  # 或其他分支名
    repo: https://github.com/yourusername/your-repo  # 替换为你的仓库地址
EOF

# 创建build脚本
print_info "创建build.sh脚本..."
cat > build.sh << 'EOF'
#!/bin/bash
set -o errexit

# 安装Python依赖
pip install --upgrade pip
pip install -r requirements.txt

# 创建必要的目录
mkdir -p logs uploads outputs
EOF

chmod +x build.sh

# 修改main.py以支持Render的端口配置
print_info "检查main.py端口配置..."
if ! grep -q "int(os.getenv('PORT'" main.py; then
    print_info "添加Render端口支持..."
    
    # 备份原文件
    cp main.py main.py.backup
    
    # 修改最后一行的端口配置
    sed -i.bak 's/uvicorn.run(app, host="0.0.0.0", port=8002)/uvicorn.run(app, host="0.0.0.0", port=int(os.getenv("PORT", 8002)))/' main.py
    
    # 确保os模块已导入
    if ! grep -q "import os" main.py; then
        sed -i.bak '1i\
import os' main.py
    fi
    
    print_success "端口配置已更新"
fi

# 创建GitHub Actions工作流（可选）
print_info "创建GitHub Actions工作流..."
mkdir -p .github/workflows
cat > .github/workflows/deploy-render.yml << 'EOF'
name: Deploy to Render

on:
  push:
    branches: [ main ]
    paths:
      - 'python-excel-service/**'
  pull_request:
    branches: [ main ]
    paths:
      - 'python-excel-service/**'

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      working-directory: ./python-excel-service
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Test application
      working-directory: ./python-excel-service
      run: |
        # 运行基本的导入测试
        python -c "import main; print('导入成功')"
    
    - name: Trigger Render Deploy
      if: github.ref == 'refs/heads/main'
      run: |
        curl -X POST "${{ secrets.RENDER_DEPLOY_HOOK_URL }}"
EOF

print_success "配置文件创建完成！"

echo ""
echo "=================================="
echo "Render部署说明"
echo "=================================="
echo ""
echo "自动部署步骤："
echo "1. 将代码推送到GitHub仓库"
echo "2. 登录 https://render.com"
echo "3. 连接GitHub仓库"
echo "4. 选择 'Web Service'"
echo "5. 配置如下设置："
echo ""
echo "基本设置："
echo "  - Name: excel-pdf-service"
echo "  - Environment: Python"
echo "  - Build Command: pip install -r requirements.txt"
echo "  - Start Command: python main.py"
echo ""
echo "环境变量："
echo "  - PYTHONUNBUFFERED=1"
echo "  - PORT=8002"
echo "  - HOST=0.0.0.0"
echo ""
echo "高级设置："
echo "  - Health Check Path: /api/health"
echo "  - Auto-Deploy: Yes"
echo ""

echo "手动部署步骤："
echo "1. 创建新的Web Service"
echo "2. 连接Git仓库: $(git remote get-url origin 2>/dev/null || echo 'your-repo-url')"
echo "3. 使用以上配置设置"
echo "4. 点击 'Create Web Service'"
echo ""

echo "部署后的URL格式："
echo "  - https://excel-pdf-service.onrender.com"
echo "  - https://your-custom-domain.com (配置自定义域名后)"
echo ""

print_warning "重要说明："
echo "  - Render免费计划在15分钟无活动后会休眠"
echo "  - 休眠后首次访问可能需要1-2分钟启动"
echo "  - 生产环境建议使用付费计划（$7/月）"
echo "  - 付费计划不会休眠且性能更好"
echo ""

echo "配置自定义域名（可选）："
echo "1. 在Render控制台添加自定义域名"
echo "2. 配置DNS CNAME记录指向 your-app.onrender.com"
echo "3. SSL证书会自动配置"
echo ""

echo "GitHub Actions自动部署（可选）："
echo "1. 在GitHub仓库设置中添加Secret: RENDER_DEPLOY_HOOK_URL"
echo "2. 从Render控制台获取Deploy Hook URL"
echo "3. 每次推送main分支会自动部署"
echo ""

print_success "Render部署配置完成！"
print_info "请按照上述说明在Render平台创建服务"