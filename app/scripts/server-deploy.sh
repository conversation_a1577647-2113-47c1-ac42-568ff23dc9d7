#!/bin/bash

# Excel转PDF Python服务 云服务器部署脚本
# 适用于: Ubuntu 18.04+, CentOS 7+, Debian 9+
# 用法: ./scripts/server-deploy.sh

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测操作系统
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$ID
        VER=$VERSION_ID
    else
        print_error "无法检测操作系统"
        exit 1
    fi
    
    print_info "检测到操作系统: $OS $VER"
}

# 安装系统依赖
install_system_deps() {
    print_info "安装系统依赖..."
    
    if [[ "$OS" == "ubuntu" || "$OS" == "debian" ]]; then
        sudo apt-get update
        sudo apt-get install -y \
            python3 \
            python3-pip \
            python3-venv \
            gcc \
            g++ \
            curl \
            git \
            nginx \
            supervisor
            
    elif [[ "$OS" == "centos" || "$OS" == "rhel" ]]; then
        sudo yum update -y
        sudo yum install -y \
            python3 \
            python3-pip \
            gcc \
            gcc-c++ \
            curl \
            git \
            nginx \
            supervisor
            
    else
        print_error "不支持的操作系统: $OS"
        exit 1
    fi
    
    print_success "系统依赖安装完成"
}

# 设置应用环境
setup_app() {
    print_info "设置应用环境..."
    
    # 创建应用目录
    APP_DIR="/opt/excel-pdf-service"
    sudo mkdir -p "$APP_DIR"
    sudo chown $(whoami):$(whoami) "$APP_DIR"
    
    # 复制应用文件
    print_info "复制应用文件到 $APP_DIR..."
    cp -r . "$APP_DIR/"
    cd "$APP_DIR"
    
    # 创建Python虚拟环境
    print_info "创建Python虚拟环境..."
    python3 -m venv .venv
    source .venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装Python依赖
    print_info "安装Python依赖..."
    pip install -r requirements.txt
    
    # 创建必要目录
    mkdir -p logs uploads outputs
    chmod 755 logs uploads outputs
    
    print_success "应用环境设置完成"
}

# 配置Nginx反向代理
configure_nginx() {
    print_info "配置Nginx反向代理..."
    
    # 创建Nginx配置
    sudo tee /etc/nginx/sites-available/excel-pdf-service > /dev/null << 'EOF'
server {
    listen 80;
    server_name api.7788.tools;  # 替换为你的API域名
    
    # 增加客户端最大body大小（用于文件上传）
    client_max_body_size 100M;
    
    # 增加超时设置
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    
    location / {
        proxy_pass http://127.0.0.1:8002;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS头设置（如果需要）
        add_header Access-Control-Allow-Origin "https://7788.tools" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Accept, Authorization, Cache-Control, Content-Type, DNT, If-Modified-Since, Keep-Alive, Origin, User-Agent, X-Requested-With" always;
        
        # 处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "https://7788.tools";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Accept, Authorization, Cache-Control, Content-Type, DNT, If-Modified-Since, Keep-Alive, Origin, User-Agent, X-Requested-With";
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type 'text/plain charset=UTF-8';
            add_header Content-Length 0;
            return 204;
        }
    }
    
    # 健康检查
    location /api/health {
        proxy_pass http://127.0.0.1:8002/api/health;
        access_log off;
    }
}
EOF
    
    # 启用站点
    sudo ln -sf /etc/nginx/sites-available/excel-pdf-service /etc/nginx/sites-enabled/
    
    # 删除默认站点（如果存在）
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # 测试Nginx配置
    sudo nginx -t
    
    # 重启Nginx
    sudo systemctl restart nginx
    sudo systemctl enable nginx
    
    print_success "Nginx配置完成"
}

# 配置Supervisor进程管理
configure_supervisor() {
    print_info "配置Supervisor进程管理..."
    
    # 创建Supervisor配置
    sudo tee /etc/supervisor/conf.d/excel-pdf-service.conf > /dev/null << EOF
[program:excel-pdf-service]
command=$APP_DIR/.venv/bin/python main.py
directory=$APP_DIR
user=$(whoami)
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=$APP_DIR/logs/service.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
environment=PYTHONPATH="$APP_DIR",PYTHONUNBUFFERED="1"
stopwaitsecs=30
killasgroup=true
stopasgroup=true
EOF
    
    # 重新加载Supervisor配置
    sudo supervisorctl reread
    sudo supervisorctl update
    
    # 启动服务
    sudo supervisorctl start excel-pdf-service
    
    print_success "Supervisor配置完成"
}

# 配置防火墙
configure_firewall() {
    print_info "配置防火墙..."
    
    if command -v ufw >/dev/null 2>&1; then
        # Ubuntu/Debian使用ufw
        sudo ufw allow 22    # SSH
        sudo ufw allow 80    # HTTP
        sudo ufw allow 443   # HTTPS
        sudo ufw --force enable
    elif command -v firewall-cmd >/dev/null 2>&1; then
        # CentOS/RHEL使用firewalld
        sudo firewall-cmd --permanent --add-service=ssh
        sudo firewall-cmd --permanent --add-service=http
        sudo firewall-cmd --permanent --add-service=https
        sudo firewall-cmd --reload
    else
        print_warning "未检测到防火墙工具，请手动配置防火墙规则"
    fi
    
    print_success "防火墙配置完成"
}

# 创建SSL证书配置（Let's Encrypt）
setup_ssl() {
    print_info "设置SSL证书..."
    
    # 安装certbot
    if [[ "$OS" == "ubuntu" || "$OS" == "debian" ]]; then
        sudo apt-get install -y certbot python3-certbot-nginx
    elif [[ "$OS" == "centos" || "$OS" == "rhel" ]]; then
        sudo yum install -y certbot python3-certbot-nginx
    fi
    
    print_info "运行以下命令获取SSL证书:"
    print_info "sudo certbot --nginx -d api.7788.tools"
    print_warning "请确保域名已正确解析到服务器IP"
}

# 创建管理脚本
create_management_scripts() {
    print_info "创建管理脚本..."
    
    # 服务管理脚本
    tee "$APP_DIR/manage-service.sh" > /dev/null << 'EOF'
#!/bin/bash

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVICE_NAME="excel-pdf-service"

case "$1" in
    start)
        echo "启动服务..."
        sudo supervisorctl start $SERVICE_NAME
        ;;
    stop)
        echo "停止服务..."
        sudo supervisorctl stop $SERVICE_NAME
        ;;
    restart)
        echo "重启服务..."
        sudo supervisorctl restart $SERVICE_NAME
        ;;
    status)
        echo "服务状态:"
        sudo supervisorctl status $SERVICE_NAME
        ;;
    logs)
        echo "查看日志:"
        tail -f $SCRIPT_DIR/logs/service.log
        ;;
    health)
        echo "健康检查:"
        curl -s http://localhost:8002/api/health | python3 -m json.tool || echo "服务不可用"
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs|health}"
        exit 1
        ;;
esac
EOF
    
    chmod +x "$APP_DIR/manage-service.sh"
    
    # 创建日志清理脚本
    tee "$APP_DIR/cleanup-logs.sh" > /dev/null << 'EOF'
#!/bin/bash
# 清理30天前的日志
find /opt/excel-pdf-service/logs -name "*.log*" -mtime +30 -delete
find /opt/excel-pdf-service/uploads -name "*" -mtime +1 -delete
find /opt/excel-pdf-service/outputs -name "*" -mtime +7 -delete
EOF
    
    chmod +x "$APP_DIR/cleanup-logs.sh"
    
    # 添加到crontab（每天清理）
    (crontab -l 2>/dev/null; echo "0 2 * * * $APP_DIR/cleanup-logs.sh") | crontab -
    
    print_success "管理脚本创建完成"
}

# 主函数
main() {
    print_info "开始部署Excel转PDF服务到云服务器..."
    
    # 检查权限
    if [ "$EUID" -eq 0 ]; then
        print_error "请不要使用root用户运行此脚本"
        exit 1
    fi
    
    # 检测操作系统
    detect_os
    
    # 安装系统依赖
    install_system_deps
    
    # 设置应用环境
    setup_app
    
    # 配置Nginx
    configure_nginx
    
    # 配置Supervisor
    configure_supervisor
    
    # 配置防火墙
    configure_firewall
    
    # 创建管理脚本
    create_management_scripts
    
    # 等待服务启动
    print_info "等待服务启动..."
    sleep 10
    
    # 健康检查
    print_info "执行健康检查..."
    if curl -s -f "http://localhost:8002/api/health" > /dev/null; then
        print_success "服务健康检查通过"
    else
        print_warning "服务可能还在启动中，请稍后检查"
    fi
    
    # 显示部署结果
    echo ""
    print_success "Excel转PDF服务部署完成！"
    echo ""
    echo "服务信息:"
    echo "  - 应用目录: $APP_DIR"
    echo "  - 内部地址: http://localhost:8002"
    echo "  - 外部地址: http://api.7788.tools (配置域名后)"
    echo "  - 健康检查: http://api.7788.tools/api/health"
    echo "  - API文档: http://api.7788.tools/docs"
    echo ""
    echo "管理命令:"
    echo "  - 服务管理: $APP_DIR/manage-service.sh {start|stop|restart|status|logs|health}"
    echo "  - 查看进程: sudo supervisorctl status excel-pdf-service"
    echo "  - 查看日志: tail -f $APP_DIR/logs/service.log"
    echo ""
    echo "下一步操作:"
    echo "  1. 将域名 api.7788.tools 解析到服务器IP"
    echo "  2. 运行 SSL 设置: sudo certbot --nginx -d api.7788.tools"
    echo "  3. 在前端配置中更新 API 地址为 https://api.7788.tools"
    echo ""
    print_success "部署完成！"
}

# 执行主函数
main