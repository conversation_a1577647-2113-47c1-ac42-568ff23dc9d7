#!/bin/bash

# Excel转PDF服务 一键部署脚本
# 用法: ./scripts/deploy.sh

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}$1${NC}"
}

# 显示欢迎信息
show_welcome() {
    clear
    print_header "=================================================="
    print_header "    Excel转PDF服务 - 一键部署脚本"
    print_header "    支持多种部署方案，适配 7788.tools 域名"
    print_header "=================================================="
    echo ""
}

# 显示部署选项
show_deployment_options() {
    echo "请选择部署方案:"
    echo ""
    echo "1. 🐳 Docker 本地部署 (推荐用于开发测试)"
    echo "   - 适用于: 本地开发、测试环境"
    echo "   - 要求: 已安装Docker"
    echo "   - 特点: 快速启动、环境隔离"
    echo ""
    echo "2. 🌐 云服务器部署 (推荐用于生产环境)"
    echo "   - 适用于: VPS、阿里云、腾讯云、AWS等"
    echo "   - 要求: Ubuntu/CentOS/Debian服务器"
    echo "   - 特点: 完整配置、Nginx反向代理、SSL支持"
    echo ""
    echo "3. 🚄 Railway 云平台 (推荐用于快速部署)"
    echo "   - 适用于: 快速原型、小型项目"
    echo "   - 要求: GitHub账号"
    echo "   - 特点: 零配置、自动扩容、免费额度"
    echo ""
    echo "4. 🎨 Render 云平台 (推荐用于稳定服务)"
    echo "   - 适用于: 生产级应用"
    echo "   - 要求: GitHub账号"
    echo "   - 特点: 自动SSL、CDN、数据库支持"
    echo ""
    echo "5. ℹ️  查看部署方案对比"
    echo ""
    echo "6. 🔧 创建环境配置文件"
    echo ""
    echo "0. 退出"
    echo ""
}

# 显示部署方案对比
show_comparison() {
    clear
    print_header "部署方案对比"
    echo ""
    echo "┌─────────────┬──────────┬──────────┬──────────┬─────────────┐"
    echo "│ 特性        │ Docker   │ 云服务器 │ Railway  │ Render      │"
    echo "├─────────────┼──────────┼──────────┼──────────┼─────────────┤"
    echo "│ 部署难度    │ 简单     │ 中等     │ 很简单   │ 简单        │"
    echo "│ 成本        │ 免费     │ $5-20/月 │ 免费/付费│ 免费/付费   │"
    echo "│ 性能        │ 高       │ 最高     │ 中等     │ 高          │"
    echo "│ 扩展性      │ 手动     │ 手动     │ 自动     │ 自动        │"
    echo "│ SSL证书     │ 需配置   │ 自动     │ 自动     │ 自动        │"
    echo "│ 自定义域名  │ 需配置   │ 支持     │ 支持     │ 支持        │"
    echo "│ 文件上传限制│ 无限制   │ 无限制   │ 100MB    │ 100MB       │"
    echo "│ 休眠机制    │ 无       │ 无       │ 有       │ 有(免费版)  │"
    echo "└─────────────┴──────────┴──────────┴──────────┴─────────────┘"
    echo ""
    echo "推荐场景:"
    echo "  • 开发测试: Docker本地部署"
    echo "  • 生产环境: 云服务器 (如果预算充足)"
    echo "  • 快速部署: Railway 或 Render (免费开始)"
    echo "  • 高并发: 云服务器 + 负载均衡"
    echo ""
    read -p "按Enter返回主菜单..."
}

# 创建环境配置文件
create_env_config() {
    print_info "创建环境配置文件..."
    
    # 创建.env文件
    cat > .env << 'EOF'
# Excel转PDF服务环境配置

# 服务配置
HOST=0.0.0.0
PORT=8002
ENV=production

# Python配置
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1

# 日志配置
LOG_LEVEL=INFO

# 文件上传配置
MAX_FILE_SIZE=100  # MB
UPLOAD_TIMEOUT=300  # 秒

# CORS配置 (用于前端域名)
ALLOWED_ORIGINS=https://7788.tools,http://localhost:3000
EOF

    # 创建.env.example文件
    cp .env .env.example
    
    # 创建docker-compose.yml
    cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  excel-pdf-service:
    build: .
    container_name: excel-pdf-service
    ports:
      - "8002:8002"
    environment:
      - PYTHONUNBUFFERED=1
      - ENV=production
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./outputs:/app/outputs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理 (可选)
  nginx:
    image: nginx:alpine
    container_name: excel-pdf-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl  # SSL证书目录
    depends_on:
      - excel-pdf-service
    restart: unless-stopped
    profiles: ["nginx"]  # 使用: docker-compose --profile nginx up
EOF

    # 创建nginx配置
    mkdir -p nginx
    cat > nginx/nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    upstream excel_pdf_backend {
        server excel-pdf-service:8002;
    }

    server {
        listen 80;
        server_name api.7788.tools;
        
        # HTTP重定向到HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name api.7788.tools;
        
        # SSL配置
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        
        # 安全头
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        
        # 文件上传大小限制
        client_max_body_size 100M;
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        location / {
            proxy_pass http://excel_pdf_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # CORS
            add_header Access-Control-Allow-Origin "https://7788.tools" always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Accept, Authorization, Cache-Control, Content-Type" always;
            
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin "https://7788.tools";
                add_header Access-Control-Max-Age 1728000;
                add_header Content-Type 'text/plain charset=UTF-8';
                add_header Content-Length 0;
                return 204;
            }
        }
    }
}
EOF

    print_success "环境配置文件创建完成!"
    echo ""
    echo "创建的文件:"
    echo "  - .env (环境变量)"
    echo "  - .env.example (环境变量示例)"
    echo "  - docker-compose.yml (Docker Compose配置)"
    echo "  - nginx/nginx.conf (Nginx配置)"
    echo ""
    echo "使用方法:"
    echo "  - Docker Compose: docker-compose up -d"
    echo "  - 带Nginx: docker-compose --profile nginx up -d"
    echo ""
    read -p "按Enter返回主菜单..."
}

# 检查脚本权限
check_permissions() {
    local script_path="$1"
    if [ ! -x "$script_path" ]; then
        print_info "设置脚本执行权限..."
        chmod +x "$script_path"
    fi
}

# 主菜单循环
main_menu() {
    while true; do
        show_welcome
        show_deployment_options
        read -p "请选择 (0-6): " choice
        
        case $choice in
            1)
                print_info "启动Docker部署..."
                check_permissions "./scripts/docker-deploy.sh"
                ./scripts/docker-deploy.sh production
                read -p "按Enter返回主菜单..."
                ;;
            2)
                print_info "启动云服务器部署..."
                check_permissions "./scripts/server-deploy.sh"
                ./scripts/server-deploy.sh
                read -p "按Enter返回主菜单..."
                ;;
            3)
                print_info "启动Railway部署..."
                check_permissions "./scripts/railway-deploy.sh"
                ./scripts/railway-deploy.sh
                read -p "按Enter返回主菜单..."
                ;;
            4)
                print_info "启动Render部署..."
                check_permissions "./scripts/render-deploy.sh"
                ./scripts/render-deploy.sh
                read -p "按Enter返回主菜单..."
                ;;
            5)
                show_comparison
                ;;
            6)
                create_env_config
                ;;
            0)
                print_success "感谢使用！"
                exit 0
                ;;
            *)
                print_error "无效选择，请重新输入"
                sleep 2
                ;;
        esac
    done
}

# 检查脚本目录
if [ ! -d "scripts" ]; then
    print_error "scripts目录不存在，请确保在正确的项目目录中运行"
    exit 1
fi

# 设置所有脚本的执行权限
for script in scripts/*.sh; do
    if [ -f "$script" ]; then
        chmod +x "$script"
    fi
done

# 启动主菜单
main_menu