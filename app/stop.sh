#!/bin/bash

# Excel转PDF服务 停止脚本
# 用法: ./stop.sh

set -e

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info "正在停止Excel转PDF服务..."

# 检查端口占用
if ! lsof -i:8002 &> /dev/null; then
    print_warning "端口8002没有被占用，服务可能已经停止"
    exit 0
fi

PID=$(lsof -t -i:8002)
PROCESS_INFO=$(ps -p $PID -o pid,ppid,comm 2>/dev/null || echo "未知进程")
print_info "找到占用端口8002的进程: $PROCESS_INFO"

# 检查是否是main.py进程
COMMAND=$(ps -p $PID -o command= 2>/dev/null || echo "")
if echo "$COMMAND" | grep -q "main.py"; then
    print_info "正在停止Excel转PDF服务 (PID: $PID)..."
    
    # 先尝试优雅停止
    kill -TERM $PID 2>/dev/null || true
    sleep 2
    
    # 检查是否已停止
    if ! ps -p $PID &> /dev/null; then
        print_success "服务已成功停止"
        exit 0
    fi
    
    # 强制停止
    print_warning "优雅停止失败，正在强制停止..."
    kill -9 $PID 2>/dev/null || true
    sleep 1
    
    # 最终检查
    if ! ps -p $PID &> /dev/null; then
        print_success "服务已强制停止"
    else
        print_error "无法停止服务进程: $PID"
        exit 1
    fi
else
    print_warning "端口8002被其他进程占用，不是Excel转PDF服务"
    print_info "占用进程: $COMMAND"
    print_info "如需强制停止，请运行: kill -9 $PID"
fi