"""
Excel转PDF服务 - Vercel部署版本（精简）
"""

from fastapi import FastAPI, UploadFile, File, HTTPException, Query
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
import os
import uuid
import aiofiles
import sys
from datetime import datetime
import logging
import tempfile

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="Excel to PDF Service",
    description="专业Excel转PDF转换服务",
    version="2.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def convert_excel_to_pdf_simple(input_path: str, output_path: str) -> Tuple[bool, str]:
    """简化的Excel转PDF转换"""
    try:
        # 延迟导入Aspose以减少启动时间
        from aspose.cells import Workbook, SaveFormat
        
        # 加载Excel文件
        workbook = Workbook(input_path)
        
        # 保存为PDF
        workbook.save(output_path, SaveFormat.PDF)
        
        return True, "转换成功"
    except Exception as e:
        logger.error(f"转换失败: {e}")
        return False, str(e)

@app.get("/api/health")
def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "platform": sys.platform,
        "service_type": "vercel_optimized"
    }

@app.post("/api/excel-to-pdf/convert")
async def convert_excel_direct(
    file: UploadFile = File(...),
    remove_watermark: bool = Query(False, description="是否移除水印")
):
    """
    直接转换Excel到PDF
    """
    
    try:
        logger.info(f"收到转换请求")
        
        # 验证文件类型
        allowed_extensions = {'.xlsx', '.xls', '.xlsb', '.xlsm'}
        file_ext = os.path.splitext(file.filename.lower())[1]
        if file_ext not in allowed_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的文件类型: {file_ext}"
            )
        
        # 使用临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as temp_input:
            content = await file.read()
            temp_input.write(content)
            temp_input_path = temp_input.name
        
        # 生成输出文件路径
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = os.path.splitext(file.filename)[0]
        pdf_filename = f"{base_name}_{timestamp}.pdf"
        
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_output:
            temp_output_path = temp_output.name
        
        # 转换
        start_time = datetime.now()
        success, message = convert_excel_to_pdf_simple(temp_input_path, temp_output_path)
        end_time = datetime.now()
        conversion_time = (end_time - start_time).total_seconds()
        
        # 清理输入文件
        try:
            os.unlink(temp_input_path)
        except:
            pass
        
        if success:
            file_size = os.path.getsize(temp_output_path) if os.path.exists(temp_output_path) else 0
            logger.info(f"转换成功: 大小: {file_size} bytes, 耗时: {conversion_time:.2f}s")
            
            # 直接返回文件
            return FileResponse(
                temp_output_path,
                media_type='application/pdf',
                filename=pdf_filename,
                headers={
                    "Content-Disposition": f"attachment; filename={pdf_filename}",
                    "Cache-Control": "no-cache",
                }
            )
        else:
            # 清理输出文件
            try:
                os.unlink(temp_output_path)
            except:
                pass
            logger.error(f"转换失败: {message}")
            raise HTTPException(status_code=500, detail=f"转换失败: {message}")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"转换过程发生异常: {e}")
        raise HTTPException(status_code=500, detail=f"转换异常: {str(e)}")

# 兼容性API
@app.post("/api/excel-to-pdf/convert-smart")
async def convert_smart_legacy(file: UploadFile = File(...)):
    """兼容性API：智能转换"""
    return await convert_excel_direct(file, remove_watermark=False)

@app.post("/api/aspose-excel-to-pdf/convert")
async def convert_aspose_legacy(file: UploadFile = File(...)):
    """兼容性API：Aspose转换"""
    return await convert_excel_direct(file, remove_watermark=False)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
