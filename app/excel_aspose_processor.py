"""
Aspose.Cells Excel转PDF处理器
提供专业级Excel转PDF转换质量
"""

import os
import sys
import logging
import tempfile
import shutil
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

# 导入PDF水印移除器 - 使用新的真正删除功能
try:
    from pdf_watermark_remover_advanced_delete import remove_watermark_truly_delete
    WATERMARK_REMOVER_AVAILABLE = True
except ImportError:
    try:
        from pdf_watermark_remover import remove_pdf_watermark
        WATERMARK_REMOVER_AVAILABLE = True
    except ImportError:
        WATERMARK_REMOVER_AVAILABLE = False

logger = logging.getLogger(__name__)

class AsposeExcelProcessor:
    """Aspose.Cells Excel转PDF处理器"""
    
    def __init__(self):
        self.available = self._check_aspose_availability()
        if self.available:
            self._apply_license()
            logger.info("Aspose.Cells Python库可用")
        else:
            logger.warning("Aspose.Cells Python库不可用")
    
    def _apply_license(self):
        """应用Aspose.Cells许可证"""
        try:
            from aspose.cells import License
            
            # 许可证文件路径
            license_path = os.path.join(os.path.dirname(__file__), "Aspose.Total.NET.lic")
            
            logger.info(f"尝试加载许可证文件: {license_path}")
            logger.info(f"许可证文件存在: {os.path.exists(license_path)}")
            
            if os.path.exists(license_path):
                license = License()
                license.set_license(license_path)
                logger.info("Aspose.Cells许可证激活成功")
                
                # 验证许可证是否生效
                try:
                    from aspose.cells import Workbook
                    wb = Workbook()
                    logger.info("许可证验证：可以创建工作簿")
                except Exception as verify_err:
                    logger.warning(f"许可证验证失败: {verify_err}")
            else:
                logger.warning(f"许可证文件不存在: {license_path}")
                
        except Exception as e:
            logger.error(f"许可证激活失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    def _check_aspose_availability(self) -> bool:
        """检查Aspose.Cells是否可用"""
        try:
            # 尝试导入Aspose.Cells
            from aspose.cells import Workbook, SaveFormat
            return True
        except ImportError:
            try:
                # 尝试旧版本的导入方式
                import aspose.cells
                return True
            except ImportError:
                logger.warning("Aspose.Cells未安装，请运行: pip install aspose-cells-python")
                return False
    
    def convert_excel_to_pdf(
        self, 
        excel_path: str, 
        pdf_path: str, 
        options: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, str]:
        """
        使用Aspose.Cells转换Excel为PDF，并可选择移除水印
        
        Args:
            excel_path: Excel文件路径
            pdf_path: 输出PDF路径
            options: 转换选项，支持:
                - remove_watermark: bool - 是否移除水印
                - watermark_height: float - 水印区域高度（像素）
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        
        if not self.available:
            return False, "Aspose.Cells库不可用"
        
        if not os.path.exists(excel_path):
            return False, f"Excel文件不存在: {excel_path}"
        
        # 解析选项
        remove_watermark = False
        watermark_height = 12.0
        watermark_method = "text_detection"  # 默认使用文字识别方法
        if options:
            remove_watermark = options.get('remove_watermark', False)
            watermark_height = options.get('watermark_height', 12.0)
            watermark_method = options.get('watermark_method', 'text_detection')
        
        try:
            from aspose.cells import Workbook
            
            logger.info(f"使用Aspose.Cells转换: {excel_path} -> {pdf_path}")
            if remove_watermark:
                logger.info("将移除PDF水印")
            
            # 加载Excel工作簿
            workbook = Workbook(excel_path)
            
            # 计算公式（如果存在）
            try:
                workbook.calculate_formula()
                logger.debug("已计算工作簿中的公式")
            except Exception as e:
                logger.warning(f"计算公式失败: {e}")
            
            # 确保输出目录存在
            output_dir = os.path.dirname(pdf_path)
            if output_dir:  # 只有当目录不为空时才创建
                os.makedirs(output_dir, exist_ok=True)
            
            # 如果需要移除水印，先转换到临时文件
            if remove_watermark and WATERMARK_REMOVER_AVAILABLE:
                with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
                    temp_pdf_path = temp_file.name
                
                # 转换到临时文件
                workbook.save(temp_pdf_path)
                
                # 移除水印 - 使用新的真正删除功能
                logger.info(f"正在移除PDF水印...方法: {watermark_method}")
                try:
                    # 优先使用真正删除功能
                    success, watermark_msg = remove_watermark_truly_delete(
                        temp_pdf_path, 
                        pdf_path,
                        method="selective"  # 使用精确匹配删除
                    )
                    logger.info(f"使用redact真正删除水印: {watermark_msg}")
                except NameError:
                    # 回退到原有方法
                    success, watermark_msg = remove_pdf_watermark(
                        temp_pdf_path, 
                        pdf_path, 
                        watermark_height,
                        watermark_method
                    )
                    logger.info(f"使用覆盖方法移除水印: {watermark_msg}")
                
                # 清理临时文件
                try:
                    os.unlink(temp_pdf_path)
                except:
                    pass
                
                if not success:
                    # 水印移除失败，使用原始文件
                    logger.warning(f"水印移除失败: {watermark_msg}，使用原始PDF")
                    shutil.move(temp_pdf_path, pdf_path)
                    return True, f"Aspose.Cells转换成功但水印移除失败: {watermark_msg}"
                else:
                    logger.info(f"水印移除成功: {watermark_msg}")
            else:
                # 直接转换
                if remove_watermark and not WATERMARK_REMOVER_AVAILABLE:
                    logger.warning("水印移除功能不可用，使用原始转换")
                
                workbook.save(pdf_path)
            
            if os.path.exists(pdf_path):
                file_size = os.path.getsize(pdf_path)
                success_msg = f"Aspose.Cells转换成功，文件大小: {file_size} bytes"
                if remove_watermark and WATERMARK_REMOVER_AVAILABLE:
                    success_msg += "（已移除水印）"
                logger.info(f"Aspose.Cells转换成功: {pdf_path}, 大小: {file_size} bytes")
                return True, success_msg
            else:
                return False, "PDF文件生成失败"
                
        except Exception as e:
            error_msg = f"Aspose.Cells转换失败: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def _apply_pdf_options(self, pdf_save_options, options: Dict[str, Any]):
        """应用PDF转换选项"""
        try:
            from aspose.cells import PdfCompliance, PdfCompressionCore
            
            # 设置页面大小
            page_size = options.get('page_size', 'A4').upper()
            if hasattr(pdf_save_options, 'page_size'):
                if page_size == 'A4':
                    pdf_save_options.page_size = 0  # A4
                elif page_size == 'A3':
                    pdf_save_options.page_size = 1  # A3
                elif page_size == 'LETTER':
                    pdf_save_options.page_size = 2  # Letter
            
            # 设置方向
            orientation = options.get('orientation', 'portrait').lower()
            if hasattr(pdf_save_options, 'orientation'):
                pdf_save_options.orientation = 0 if orientation == 'portrait' else 1
            
            # 设置质量和压缩
            quality = options.get('quality', 'High').lower()
            if quality == 'high':
                if hasattr(pdf_save_options, 'jpeg_quality'):
                    pdf_save_options.jpeg_quality = 100
                if hasattr(pdf_save_options, 'compression_type'):
                    pdf_save_options.compression_type = PdfCompressionCore.NONE
            elif quality == 'medium':
                if hasattr(pdf_save_options, 'jpeg_quality'):
                    pdf_save_options.jpeg_quality = 80
            elif quality == 'low':
                if hasattr(pdf_save_options, 'jpeg_quality'):
                    pdf_save_options.jpeg_quality = 60
            
            # PDF合规性
            compliance = options.get('compliance', 'pdf15').lower()
            if hasattr(pdf_save_options, 'compliance'):
                if compliance == 'pdf15':
                    pdf_save_options.compliance = PdfCompliance.PDF15
                elif compliance == 'pdfa1a':
                    pdf_save_options.compliance = PdfCompliance.PDF_A_1_A
                elif compliance == 'pdfa1b':
                    pdf_save_options.compliance = PdfCompliance.PDF_A_1_B
            
            # 是否包含网格线
            show_gridlines = options.get('show_gridlines', True)
            if hasattr(pdf_save_options, 'print_grid_lines'):
                pdf_save_options.print_grid_lines = show_gridlines
            
            # 是否适应页面
            fit_to_page = options.get('fit_to_page', True)
            if hasattr(pdf_save_options, 'all_columns_in_one_page_per_sheet'):
                pdf_save_options.all_columns_in_one_page_per_sheet = fit_to_page
            
            # 字体嵌入
            embed_fonts = options.get('embed_fonts', True)
            if hasattr(pdf_save_options, 'embed_standard_windows_fonts'):
                pdf_save_options.embed_standard_windows_fonts = embed_fonts
            
            logger.debug(f"已应用PDF转换选项: {options}")
            
        except Exception as e:
            logger.warning(f"应用PDF选项失败: {e}")
    
    def convert_specific_sheets(
        self, 
        excel_path: str, 
        pdf_path: str, 
        sheet_names: List[str],
        options: Optional[Dict[str, Any]] = None
    ) -> Tuple[bool, str]:
        """
        转换指定的工作表为PDF
        
        Args:
            excel_path: Excel文件路径
            pdf_path: 输出PDF路径
            sheet_names: 要转换的工作表名称列表
            options: 转换选项
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        
        if not self.available:
            return False, "Aspose.Cells库不可用"
        
        try:
            from aspose.cells import Workbook
            from aspose.cells.rendering import PdfSaveOptions, SheetRender, ImageOrPrintOptions
            
            # 加载工作簿
            workbook = Workbook(excel_path)
            
            # 计算公式
            try:
                workbook.calculate_formula()
            except Exception as e:
                logger.warning(f"计算公式失败: {e}")
            
            # 隐藏不需要的工作表
            for i in range(workbook.worksheets.count):
                worksheet = workbook.worksheets[i]
                if worksheet.name not in sheet_names:
                    worksheet.is_visible = False
                else:
                    worksheet.is_visible = True
            
            # 创建PDF保存选项
            pdf_save_options = PdfSaveOptions()
            if options:
                self._apply_pdf_options(pdf_save_options, options)
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(pdf_path), exist_ok=True)
            
            # 保存为PDF
            workbook.save(pdf_path, pdf_save_options)
            
            if os.path.exists(pdf_path):
                file_size = os.path.getsize(pdf_path)
                return True, f"指定工作表转换成功，文件大小: {file_size} bytes"
            else:
                return False, "PDF文件生成失败"
                
        except Exception as e:
            error_msg = f"转换指定工作表失败: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def get_workbook_info(self, excel_path: str) -> Dict[str, Any]:
        """
        获取Excel工作簿信息
        
        Args:
            excel_path: Excel文件路径
            
        Returns:
            工作簿信息字典
        """
        
        if not self.available:
            return {"error": "Aspose.Cells库不可用"}
        
        try:
            from aspose.cells import Workbook
            
            workbook = Workbook(excel_path)
            
            # 收集工作表信息
            sheets_info = []
            for i in range(workbook.worksheets.count):
                worksheet = workbook.worksheets[i]
                
                # 获取使用范围
                try:
                    cells = worksheet.cells
                    max_row = cells.max_data_row + 1
                    max_col = cells.max_data_column + 1
                    used_range = f"A1:{cells.get_cell(max_row-1, max_col-1).name}"
                except:
                    used_range = "未知"
                
                sheet_info = {
                    "name": worksheet.name,
                    "index": i,
                    "visible": worksheet.is_visible,
                    "used_range": used_range,
                    "max_row": max_row if 'max_row' in locals() else 0,
                    "max_column": max_col if 'max_col' in locals() else 0
                }
                sheets_info.append(sheet_info)
            
            return {
                "file_path": excel_path,
                "total_sheets": workbook.worksheets.count,
                "sheets": sheets_info,
                "file_format": workbook.file_format.name if hasattr(workbook.file_format, 'name') else "未知"
            }
            
        except Exception as e:
            logger.error(f"获取工作簿信息失败: {e}")
            return {"error": str(e)}

# 创建全局实例
aspose_processor = AsposeExcelProcessor()

def is_aspose_available() -> bool:
    """检查Aspose.Cells是否可用"""
    return aspose_processor.available

def convert_excel_to_pdf_aspose(
    excel_path: str,
    pdf_path: str,
    options: Optional[Dict[str, Any]] = None
) -> Tuple[bool, str]:
    """
    使用Aspose.Cells转换Excel为PDF的便捷函数
    
    Args:
        excel_path: Excel文件路径
        pdf_path: 输出PDF路径
        options: 转换选项
        
    Returns:
        Tuple[bool, str]: (是否成功, 消息)
    """
    return aspose_processor.convert_excel_to_pdf(excel_path, pdf_path, options)

# 示例使用
if __name__ == "__main__":
    # 测试Aspose.Cells可用性
    print(f"Aspose.Cells可用: {is_aspose_available()}")
    
    # 如果可用，进行转换测试
    if is_aspose_available():
        test_excel = "test.xlsx"
        test_pdf = "output_aspose.pdf"
        
        if os.path.exists(test_excel):
            success, message = convert_excel_to_pdf_aspose(
                test_excel, 
                test_pdf,
                {
                    'quality': 'High',
                    'page_size': 'A4',
                    'orientation': 'portrait',
                    'show_gridlines': True,
                    'fit_to_page': True,
                    'embed_fonts': True
                }
            )
            print(f"转换结果: {message}")
        else:
            print("测试文件不存在")
    else:
        print("请先安装Aspose.Cells: pip install aspose-cells-python")