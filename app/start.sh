#!/bin/bash

# Excel转PDF服务 快速启动脚本
# 用法: ./start.sh

set -e

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info "启动Excel转PDF服务..."

# 检查Python版本
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    print_error "Python未安装，请先安装Python 3.8+"
    exit 1
fi

# 使用python3或python
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
else
    PYTHON_CMD="python"
fi

print_info "使用Python命令: $PYTHON_CMD"

# 检查Python版本
PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | grep -oE '[0-9]+\.[0-9]+')
MAJOR_VERSION=$(echo $PYTHON_VERSION | cut -d. -f1)
MINOR_VERSION=$(echo $PYTHON_VERSION | cut -d. -f2)

if [ "$MAJOR_VERSION" -lt 3 ] || ([ "$MAJOR_VERSION" -eq 3 ] && [ "$MINOR_VERSION" -lt 8 ]); then
    print_error "Python版本过低: $PYTHON_VERSION，需要3.8或更高版本"
    exit 1
fi

print_success "Python版本检查通过: $PYTHON_VERSION"

# 检查端口占用
if lsof -i:8002 &> /dev/null; then
    print_warning "检测到端口8002已被占用"
    PID=$(lsof -t -i:8002)
    PROCESS_INFO=$(ps -p $PID -o pid,ppid,comm 2>/dev/null || echo "未知进程")
    print_info "占用进程信息: $PROCESS_INFO"
    
    # 检查是否是相同的main.py进程
    if ps -p $PID -o command= 2>/dev/null | grep -q "python.*main.py"; then
        print_success "Excel转PDF服务已经在运行中！"
        print_info "进程ID: $PID"
        print_info "服务地址: http://localhost:8002"
        print_info "健康检查: http://localhost:8002/api/health"
        print_info "API文档: http://localhost:8002/docs"
        print_info ""
        print_info "要停止服务，请运行: kill $PID"
        exit 0
    else
        print_warning "端口被其他进程占用，正在尝试释放..."
        kill -9 $PID 2>/dev/null || true
        sleep 3
        
        # 再次检查端口是否被释放
        if lsof -i:8002 &> /dev/null; then
            print_error "无法释放端口8002，请手动停止占用进程: $PID"
            print_info "命令: kill -9 $PID"
            exit 1
        fi
        print_success "端口8002已释放"
    fi
fi

# 检查requirements.txt
if [ ! -f "requirements.txt" ]; then
    print_error "requirements.txt文件不存在"
    exit 1
fi

# 安装依赖（首次运行或依赖更新时）
print_info "检查Python依赖..."
if ! $PYTHON_CMD -c "import fastapi, uvicorn" &> /dev/null; then
    print_info "安装Python依赖..."
    $PYTHON_CMD -m pip install --upgrade pip
    $PYTHON_CMD -m pip install -r requirements.txt
    print_success "依赖安装完成"
else
    print_success "依赖检查通过"
fi

# 创建必要目录
mkdir -p logs uploads outputs
print_info "创建运行目录: logs, uploads, outputs"

# 启动服务
print_info "启动Excel转PDF服务..."
print_info "服务地址: http://localhost:8002"
print_info "健康检查: http://localhost:8002/api/health"
print_info "API文档: http://localhost:8002/docs"
print_info ""
print_warning "按 Ctrl+C 停止服务"
print_info ""

# 启动Python服务
exec $PYTHON_CMD main.py