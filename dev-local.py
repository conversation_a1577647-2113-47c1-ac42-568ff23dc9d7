#!/usr/bin/env python3
"""
本地开发服务器
使用FastAPI运行完整功能版本
"""

import uvicorn
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

if __name__ == "__main__":
    try:
        from main import app
        if app is not None:
            print("🚀 启动本地开发服务器...")
            print("📝 API文档: http://localhost:8000/docs")
            print("🔍 健康检查: http://localhost:8000/api/health")
            uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
        else:
            print("❌ FastAPI不可用，请安装依赖: pip install -r requirements-cloudflare.txt")
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请安装依赖: pip install -r requirements-cloudflare.txt")
